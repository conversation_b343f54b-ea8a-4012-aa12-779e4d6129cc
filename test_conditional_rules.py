#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试条件性规则应用
验证强制性规则只在有引用意图时才应用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path.cwd()))

def test_first_round_normal():
    """测试第一轮对话（无引用）"""
    print("🔧 测试第一轮对话（无引用意图）")
    print("=" * 60)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        
        # 模拟LLM响应
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
                self.tokens_used = 500
        
        # 模拟底层client
        class MockTongyiClient:
            def call(self, instruction, context="", **kwargs):
                print(f"📝 第一轮LLM收到提示词，长度: {len(instruction)} 字符")
                
                # 检查是否错误应用了强制性规则
                has_critical_rules = "🚨🚨🚨 CRITICAL" in instruction
                has_mandatory_rules = "MANDATORY RULE" in instruction
                has_reference_warning = "当前用户指令包含引用关键词" in instruction
                
                print(f"   强制性规则: {'❌ 错误应用' if has_critical_rules else '✅ 未应用'}")
                print(f"   必须规则: {'❌ 错误应用' if has_mandatory_rules else '✅ 未应用'}")
                print(f"   引用警告: {'❌ 错误应用' if has_reference_warning else '✅ 未应用'}")
                
                # 生成正常的地区分析代码
                normal_code = '''
import pandas as pd
import streamlit as st

# 2024年各地区销售额分析
region_sales = df.groupby('地区')['销售额'].sum().reset_index()

st.subheader("📊 2024年各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")

# 显示详细数据
st.write("详细数据:")
st.dataframe(region_sales)
                '''
                
                return MockLLMResponse(normal_code)
        
        # 创建LLM实例
        enhanced_llm = EnhancedTongyiLLM(
            client=MockTongyiClient(),
            enable_logging=True
        )
        
        # 第一轮对话上下文（无引用）
        conversation_context = {
            'recent_rounds': [],
            'references': {}  # 空的引用
        }
        
        # 测试第一轮指令
        instruction = "2024年各地区销售额"
        context = "数据包含地区、销售员、销售额等字段"
        
        print(f"📝 测试指令: {instruction}")
        print("-" * 40)
        
        # 调用分析方法
        result_code = enhanced_llm.analyze_data_with_context(
            instruction=instruction,
            context=context,
            conversation_context=conversation_context,
            metadata=None,
            table_name="sales_data"
        )
        
        print(f"\n📊 第一轮代码分析:")
        print("-" * 40)
        
        # 验证第一轮代码质量
        checks_passed = 0
        total_checks = 4
        
        if 'region_sales = df.groupby(' in result_code:
            print("✅ 检查1通过: 正确定义了region_sales变量")
            checks_passed += 1
        else:
            print("❌ 检查1失败: 没有定义region_sales变量")
        
        if 'st.bar_chart' in result_code or 'st.dataframe' in result_code:
            print("✅ 检查2通过: 包含适当的可视化")
            checks_passed += 1
        else:
            print("❌ 检查2失败: 缺少可视化")
        
        if "if 'region_sales' not in locals():" not in result_code:
            print("✅ 检查3通过: 没有错误的变量复用检查")
            checks_passed += 1
        else:
            print("❌ 检查3失败: 错误应用了变量复用规则")
        
        if 'region_salesperson' not in result_code:
            print("✅ 检查4通过: 没有错误的组合分析")
            checks_passed += 1
        else:
            print("❌ 检查4失败: 错误应用了组合分析规则")
        
        print(f"\n📋 第一轮代码质量: {checks_passed}/{total_checks} 通过")
        
        return checks_passed >= 3, result_code
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, ""

def test_second_round_with_reference():
    """测试第二轮对话（有引用）"""
    print(f"\n🔧 测试第二轮对话（有引用意图）")
    print("=" * 60)
    
    try:
        from core.llm.llm_factory import EnhancedTongyiLLM
        
        # 模拟LLM响应
        class MockLLMResponse:
            def __init__(self, content):
                self.content = content
                self.tokens_used = 500
        
        # 模拟底层client
        class MockTongyiClient:
            def call(self, instruction, context="", **kwargs):
                print(f"📝 第二轮LLM收到提示词，长度: {len(instruction)} 字符")
                
                # 检查是否正确应用了强制性规则
                has_critical_rules = "🚨🚨🚨 CRITICAL" in instruction
                has_mandatory_rules = "MANDATORY RULE" in instruction
                has_reference_warning = "当前用户指令包含引用关键词" in instruction
                
                print(f"   强制性规则: {'✅ 正确应用' if has_critical_rules else '❌ 未应用'}")
                print(f"   必须规则: {'✅ 正确应用' if has_mandatory_rules else '❌ 未应用'}")
                print(f"   引用警告: {'✅ 正确应用' if has_reference_warning else '❌ 未应用'}")
                
                # 故意生成错误代码测试验证机制
                wrong_code = '''
import pandas as pd
import streamlit as st

# 独立的销售员分析（故意错误）
salesperson_sales = df.groupby('销售员')['销售额'].sum().reset_index()
st.bar_chart(salesperson_sales, x='销售员', y='销售额')
                '''
                
                return MockLLMResponse(wrong_code)
        
        # 创建LLM实例
        enhanced_llm = EnhancedTongyiLLM(
            client=MockTongyiClient(),
            enable_logging=True
        )
        
        # 第二轮对话上下文（有引用）
        conversation_context = {
            'recent_rounds': [
                {
                    'user_message': '2024年各地区销售额',
                    'code': '''
import pandas as pd
import streamlit as st
region_sales = df.groupby('地区')['销售额'].sum().reset_index()
st.subheader("📊 2024年各地区销售额分析")
st.bar_chart(region_sales, x="地区", y="销售额")
                    ''',
                    'execution_result': {'success': True}
                }
            ],
            'references': {
                'analysis': {
                    'code': 'region_sales = df.groupby("地区")["销售额"].sum().reset_index()',
                    'success': True
                }
            }
        }
        
        # 测试第二轮指令（包含引用关键词）
        instruction = "进一步分析销售员的销售情况"
        context = "数据包含地区、销售员、销售额等字段"
        
        print(f"📝 测试指令: {instruction}")
        print("-" * 40)
        
        # 调用分析方法
        result_code = enhanced_llm.analyze_data_with_context(
            instruction=instruction,
            context=context,
            conversation_context=conversation_context,
            metadata=None,
            table_name="sales_data"
        )
        
        print(f"\n📊 第二轮代码分析:")
        print("-" * 40)
        
        # 验证第二轮代码质量
        checks_passed = 0
        total_checks = 4
        
        if "if 'region_sales' not in locals():" in result_code:
            print("✅ 检查1通过: 正确复用了region_sales变量")
            checks_passed += 1
        else:
            print("❌ 检查1失败: 没有复用region_sales变量")
        
        if "groupby(['地区', '销售员'])" in result_code or 'groupby(["地区", "销售员"])' in result_code:
            print("✅ 检查2通过: 实现了组合分析")
            checks_passed += 1
        else:
            print("❌ 检查2失败: 没有实现组合分析")
        
        if "for region in region_sales['地区']:" in result_code or 'for region in region_sales["地区"]:' in result_code:
            print("✅ 检查3通过: 基于region_sales进行展示")
            checks_passed += 1
        else:
            print("❌ 检查3失败: 没有基于region_sales展示")
        
        if 'salesperson_sales = df.groupby(' not in result_code:
            print("✅ 检查4通过: 避免了独立的销售员分析")
            checks_passed += 1
        else:
            print("❌ 检查4失败: 仍然是独立的销售员分析")
        
        print(f"\n📋 第二轮代码质量: {checks_passed}/{total_checks} 通过")
        
        return checks_passed >= 3
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 条件性规则应用测试")
    print("=" * 80)
    
    # 测试第一轮（无引用）
    first_success, first_code = test_first_round_normal()
    
    # 测试第二轮（有引用）
    second_success = test_second_round_with_reference()
    
    print(f"\n📊 测试结果总结:")
    print("=" * 60)
    print(f"  第一轮（无引用）: {'✅ 通过' if first_success else '❌ 失败'}")
    print(f"  第二轮（有引用）: {'✅ 通过' if second_success else '❌ 失败'}")
    
    if first_success and second_success:
        print("\n🎉 条件性规则应用修复成功！")
        print("✅ 第一轮对话不再错误应用强制性规则")
        print("✅ 第二轮对话正确应用强制性规则")
        print("✅ 验证机制确保代码质量")
        print("💡 请重启应用并重新测试您的两轮对话")
    else:
        print("\n⚠️ 仍需进一步调试")
    
    print(f"\n🎯 关键修复:")
    print("现在强制性规则只在检测到引用意图且有历史引用时才应用！")
    print("这确保第一轮对话能正常生成region_sales变量。")
